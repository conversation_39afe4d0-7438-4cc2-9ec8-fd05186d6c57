import { Component, Input } from '@angular/core';

@Component({
	selector: 'ngx-workspace-loading-overlay',
	template: `
		<div class="workspace-loading-overlay" [class.visible]="visible">
			<div class="loading-content">
				<div class="spinner-container">
					<div class="spinner-circle"></div>
					<div class="spinner-circle"></div>
					<div class="spinner-circle"></div>
				</div>
				<p>{{ message }}</p>
			</div>
		</div>
	`,
	styleUrls: ['./workspace-loading-overlay.component.scss'],
	standalone: true
})
export class WorkspaceLoadingOverlayComponent {
	@Input() visible: boolean = false;
	@Input() message: string = 'Switching workspace...';
}
