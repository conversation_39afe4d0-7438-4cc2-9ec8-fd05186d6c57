.workspace-loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	opacity: 0;
	visibility: hidden;
	transition: opacity 0.3s ease, visibility 0.3s ease;
	
	// Use the existing backdrop-blur class from the app
	backdrop-filter: blur(8px);
	background-color: rgba(0, 0, 0, 0.3);

	&.visible {
		opacity: 1;
		visibility: visible;
	}

	.loading-content {
		text-align: center;
		padding: 2rem;
		background: var(--gauzy-card-1);
		border-radius: 12px;
		box-shadow: var(--gauzy-shadow);
		max-width: 300px;

		.spinner-container {
			display: flex;
			justify-content: center;
			gap: 0.5rem;
			margin-bottom: 1.5rem;

			.spinner-circle {
				width: 12px;
				height: 12px;
				background: var(--color-primary-500);
				border-radius: 50%;
				animation: pulse 1.5s infinite ease-in-out;

				&:nth-child(2) {
					animation-delay: 0.2s;
				}

				&:nth-child(3) {
					animation-delay: 0.4s;
				}
			}
		}

		p {
			margin: 0;
			color: var(--gauzy-text-color-1);
			font-size: 0.9rem;
			font-weight: 500;
		}
	}
}

@keyframes pulse {
	0%, 80%, 100% {
		transform: scale(0.8);
		opacity: 0.5;
	}
	40% {
		transform: scale(1);
		opacity: 1;
	}
}
