<div class="workspaces-container">
	<!-- Loading indicator -->
	<div *ngIf="loading" class="loading-container">
		<nb-spinner size="small"></nb-spinner>
	</div>

	<!-- Workspaces list -->
	<div
		*ngFor="let workspace of workspaces$ | async; index as i"
		class="workspace-item"
		[ngClass]="{
			selected: selected?.id === workspace.id,
			loading: loading
		}"
	>
		<div class="img-container" [title]="workspace.name" (click)="onChangeWorkspace(workspace)">
			<img
				[src]="workspace.imgUrl"
				[alt]="workspace.name"
				(error)="$event.target.src = '/assets/images/default-workspace.svg'"
			/>
			<div *ngIf="workspace.isOnline" class="online-indicator"></div>
			<div *ngIf="loading && selected?.id === workspace.id" class="loading-overlay">
				<nb-spinner size="tiny"></nb-spinner>
			</div>
		</div>
	</div>

	<!-- Add workspace button -->
	<div class="add-workspace">
		<button nbButton [nbContextMenu]="contextMenus" nbContextMenuPlacement="right" outline [disabled]="loading">
			<nb-icon icon="plus-outline"></nb-icon>
		</button>
	</div>

	<!-- Error message -->
	<div *ngIf="error" class="error-message">
		<nb-icon icon="alert-triangle-outline" status="danger"></nb-icon>
	</div>
</div>
