import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { IAuthResponse } from '@gauzy/contracts';
import { Store, AuthService, ToastrService, AppInitService } from '@gauzy/ui-core/core';

/**
 * Service for handling workspace switching with complete reset
 */
@Injectable({
	providedIn: 'root'
})
export class WorkspaceResetService {
	constructor(
		private readonly store: Store,
		private readonly authService: AuthService,
		private readonly toastrService: ToastrService,
		private readonly appInitService: AppInitService
	) {}

	/**
	 * Switch workspace with proper reinitialization (following AppInitService pattern)
	 *
	 * @param workspaceId The ID of the workspace to switch to
	 * @returns Observable with the auth response
	 */
	public switchWorkspace(workspaceId: string): Observable<IAuthResponse> {
		// 1. Save important user preferences before reset
		const preferredLanguage = this.store.preferredLanguage;
		const themeName = localStorage.getItem('themeName');

		// 2. Call API to switch workspace FIRST (using current token)
		return this.authService.switchWorkspace(workspaceId).pipe(
			tap(async (response: IAuthResponse) => {
				if (!response) {
					throw new Error('Failed to switch workspace');
				}

				// 3. Update store with new workspace data (like auth-strategy.login)
				this.updateStoreWithNewWorkspace(response);

				// 4. Restore user preferences
				this.store.preferredLanguage = preferredLanguage;
				if (themeName) {
					localStorage.setItem('themeName', themeName);
				}

				// 5. Reinitialize the application properly (like AppInitService.init)
				await this.reinitializeApplication();
			}),
			catchError((error) => {
				console.error('Error switching workspace:', error);
				this.toastrService.danger('Failed to switch workspace. Please try again.', 'Error');
				throw error;
			})
		);
	}

	/**
	 * Update store with new workspace data (like auth-strategy.login)
	 */
	private updateStoreWithNewWorkspace(response: IAuthResponse): void {
		const { user, token, refresh_token } = response;

		// Update store exactly like auth-strategy.login (lines 315-320)
		this.store.userId = user.id;
		this.store.token = token;
		this.store.refresh_token = refresh_token;
		this.store.organizationId = user?.employee?.organizationId;
		this.store.tenantId = user?.tenantId;
		this.store.user = user;
	}

	/**
	 * Reinitialize the application properly (following AppInitService.init pattern)
	 */
	private async reinitializeApplication(): Promise<void> {
		try {
			// Use the existing AppInitService to reinitialize everything properly
			// This will:
			// 1. Call usersService.getMe() to get full user data with relations
			// 2. Update store.user with complete data
			// 3. Load permissions via permissionsService.loadPermissions()
			// 4. Set up featureTenant
			await this.appInitService.init();

			// Success notification
			this.toastrService.success('Workspace switched successfully', 'Success');
		} catch (error) {
			console.error('Error reinitializing application:', error);
			this.toastrService.danger('Failed to reinitialize application after workspace switch', 'Error');
			throw error;
		}
	}
}
