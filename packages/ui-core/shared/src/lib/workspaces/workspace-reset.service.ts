import { Injectable, Optional, Injector } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { IAuthResponse } from '@gauzy/contracts';
import {
	Store,
	AuthService,
	ToastrService,
	AppInitService,
	ElectronService,
	TimeTrackerService,
	TimesheetFilterService,
	OrganizationStore,
	OrganizationProjectAkitaStore,
	PermissionsService
} from '@gauzy/ui-core/core';
import { NgxPermissionsService } from 'ngx-permissions';
import { ChangeDetectorRef, ApplicationRef } from '@angular/core';
import { deleteCookie } from '../../../../core/src/lib/auth/cookie-helper';

/**
 * Service for handling workspace switching with complete reset
 */
@Injectable({
	providedIn: 'root'
})
export class WorkspaceResetService {
	constructor(
		private readonly store: Store,
		private readonly authService: AuthService,
		private readonly toastrService: ToastrService,
		private readonly appInitService: AppInitService,
		private readonly electronService: ElectronService,
		private readonly timeTrackerService: TimeTrackerService,
		private readonly timesheetFilterService: TimesheetFilterService,
		private readonly organizationStore: OrganizationStore,
		private readonly organizationProjectStore: OrganizationProjectAkitaStore,
		private readonly permissionsService: PermissionsService,
		private readonly ngxPermissionsService: NgxPermissionsService,
		private readonly applicationRef: ApplicationRef,
		private readonly injector: Injector
	) {}

	/**
	 * Switch workspace with app remount effect
	 *
	 * @param workspaceId The ID of the workspace to switch to
	 * @returns Observable with the auth response
	 */
	public switchWorkspace(workspaceId: string): Observable<IAuthResponse> {
		// 1. Save important user preferences before reset
		const preferredLanguage = this.store.preferredLanguage;
		const themeName = localStorage.getItem('themeName');

		// 2. Call API to switch workspace FIRST (using current token)
		return this.authService.switchWorkspace(workspaceId).pipe(
			tap(async (response: IAuthResponse) => {
				if (!response) {
					throw new Error('Failed to switch workspace');
				}

				// 3. Create "app remount"
				await this.remountApplication(response, preferredLanguage, themeName);
			}),
			catchError((error) => {
				console.error('Error switching workspace:', error);
				this.toastrService.danger('Failed to switch workspace. Please try again.', 'Error');
				throw error;
			})
		);
	}

	/**
	 * Complete store reset - removes everything
	 */
	private async completeStoreReset(): Promise<void> {
		// 1. Clear time tracking and timesheet filter before reset
		await this.clearTimeTrackingData();

		// 2. Standard store reset
		this.store.clear();

		// 3. Manual reset of all important properties
		// Auth related
		this.store.token = null;
		this.store.refresh_token = null;
		this.store.userId = null;
		this.store.user = null;
		this.store.userRolePermissions = null;

		// Organization related
		this.store.organizationId = null;
		this.store.selectedOrganization = null;

		// Tenant related
		this.store.tenantId = null;

		// Employee related
		this.store.selectedEmployee = null;

		// Project related
		this.store.selectedProject = null;

		// Team related
		this.store.selectedTeam = null;

		// 4. Clear specialized Akita stores
		this.clearAkitaStores();

		// 5. Clear features and permissions
		this.clearFeaturesAndPermissions();

		// 6. Clean localStorage/sessionStorage related to store
		this.clearStorageData();

		// 7. Clear cache services
		this.clearCacheServices();

		// 8. Clean authentication cookies
		this.clearAuthenticationCookies();

		// 9. Force Angular change detection
		setTimeout(() => {
			console.log('Store reset completed - forcing change detection');
		}, 0);
	}

	/**
	 * Clear time tracking data before workspace switch
	 */
	private async clearTimeTrackingData(): Promise<void> {
		if (this.store.user && this.store.user.employee) {
			// Stop time tracking if running
			if (this.timeTrackerService.running) {
				if (this.timeTrackerService.timerSynced.isExternalSource) {
					this.timeTrackerService.remoteToggle();
				} else {
					await this.timeTrackerService.toggle();
				}
			}

			// Clear time tracker and timesheet filter
			this.timeTrackerService.clearTimeTracker();
			this.timesheetFilterService.clear();
		}
	}

	/**
	 * Clear authentication cookies
	 */
	private clearAuthenticationCookies(): void {
		deleteCookie('userId', { SameSite: 'None', Secure: true });
		deleteCookie('token', { SameSite: 'None', Secure: true });
		deleteCookie('refresh_token', { SameSite: 'None', Secure: true });
	}

	/**
	 * Clear localStorage/sessionStorage data related to store
	 */
	private clearStorageData(): void {
		// List of keys to preserve
		const keysToPreserve = ['themeName', 'preferredLanguage', 'serverConnection'];

		// Get all keys
		const allKeys = Object.keys(localStorage);

		// Remove all keys except those to preserve
		allKeys.forEach((key) => {
			if (!keysToPreserve.includes(key)) {
				localStorage.removeItem(key);
			}
		});

		// Same for sessionStorage if needed
		const sessionKeys = Object.keys(sessionStorage);
		sessionKeys.forEach((key) => {
			if (!keysToPreserve.includes(key)) {
				sessionStorage.removeItem(key);
			}
		});
	}

	/**
	 * Handle Electron authentication after workspace switch
	 */
	private electronAuthentication(response: IAuthResponse): void {
		try {
			if (this.electronService.isElectron) {
				const { user, token } = response;
				this.electronService.ipcRenderer.send('auth_success', {
					user: user,
					token: token,
					userId: user.id,
					employeeId: user.employee ? user.employee.id : null,
					organizationId: user.employee ? user.employee.organizationId : null,
					tenantId: user.tenantId ? user.tenantId : null
				});
			}
		} catch (error) {
			console.error('Electron authentication error:', error);
		}
	}

	/**
	 * Remount the application with Slack-like effect
	 * This creates the illusion of the app being completely restarted
	 */
	private async remountApplication(
		response: IAuthResponse,
		preferredLanguage: string,
		themeName: string
	): Promise<void> {
		try {
			// 1. Complete store reset (not just clear())
			await this.completeStoreReset();

			// 2. Update store with new workspace data (like auth-strategy.login)
			const { user, token, refresh_token } = response;
			this.store.userId = user.id;
			this.store.token = token;
			this.store.refresh_token = refresh_token;
			this.store.organizationId = user?.employee?.organizationId;
			this.store.tenantId = user?.tenantId;
			this.store.user = user;

			// 3. Restore user preferences
			this.store.preferredLanguage = preferredLanguage;
			if (themeName) {
				localStorage.setItem('themeName', themeName);
			}

			// 4. Handle Electron authentication
			this.electronAuthentication(response);

			// 5. Reinitialize the application properly (like AppInitService.init)
			await this.appInitService.init();

			// 6. CRITICAL: Force reload permissions after init (this ensures UI updates)
			await this.forcePermissionsReload();

			// 7. Force garbage collection if possible
			if (window.gc) {
				window.gc();
			}

			// 8. Success notification
			this.toastrService.success('Workspace switched successfully', 'Success');
		} catch (error) {
			console.error('Error remounting application:', error);
			this.toastrService.danger('Failed to switch workspace', 'Error');
			throw error;
		}
	}

	/**
	 * Clear specialized Akita stores that may persist workspace-specific data
	 */
	private clearAkitaStores(): void {
		try {
			// Reset organization-related stores
			this.organizationStore.reset();
			this.organizationProjectStore.reset();

			// Clear Akita store data from localStorage
			this.clearAkitaLocalStorage();

			console.log('Akita stores cleared successfully');
		} catch (error) {
			console.warn('Error clearing Akita stores:', error);
		}
	}

	/**
	 * Clear Akita-related data from localStorage
	 */
	private clearAkitaLocalStorage(): void {
		try {
			const akitaPatterns = ['AkitaStores', 'akita', '@datorama', 'organization', 'project', 'app', 'persist'];

			const allKeys = Object.keys(localStorage);
			let clearedCount = 0;

			allKeys.forEach((key) => {
				akitaPatterns.forEach((pattern) => {
					if (
						key.toLowerCase().includes(pattern.toLowerCase()) &&
						!['themeName', 'preferredLanguage', 'serverConnection'].includes(key)
					) {
						localStorage.removeItem(key);
						clearedCount++;
					}
				});
			});

			console.log(`Cleared ${clearedCount} Akita-related entries from localStorage`);
		} catch (error) {
			console.warn('Error clearing Akita localStorage:', error);
		}
	}

	/**
	 * Clear features and permissions that may be workspace-specific
	 */
	private clearFeaturesAndPermissions(): void {
		// Clear feature-related data
		this.store.featureToggles = [];
		this.store.featureOrganizations = [];
		this.store.featureTenant = [];

		// Clear permissions from store
		this.store.userRolePermissions = [];

		// CRITICAL: Clear NgxPermissionsService (this is what was missing!)
		this.ngxPermissionsService.flushPermissions();

		console.log('Features and permissions cleared (including NgxPermissionsService)');
	}

	/**
	 * Clear ALL cache services that may hold workspace-specific data
	 */
	private clearCacheServices(): void {
		try {
			// Clear timesheet filter service
			this.timesheetFilterService.clear();

			// Clear time tracker service
			this.timeTrackerService.clearTimeTracker();

			// Clear ALL AbstractCacheService-based services
			this.clearAllAbstractCacheServices();

			console.log('All cache services cleared successfully');
		} catch (error) {
			console.warn('Error clearing cache services:', error);
		}
	}

	/**
	 * Clear all services that extend AbstractCacheService
	 * Using a more aggressive approach: clear localStorage with cache prefixes
	 */
	private clearAllAbstractCacheServices(): void {
		try {
			// Strategy 1: Clear localStorage keys that match cache service patterns
			const cacheServicePrefixes = [
				'OrganizationsCacheService',
				'ProjectCacheService',
				'TeamsCacheService',
				'EmployeeCacheService',
				'ClientCacheService',
				'TaskCacheService',
				'TaskStatusCacheService',
				'TaskPriorityCacheService',
				'TaskSizeCacheService',
				'TaskStatisticsCacheService',
				'TagCacheService',
				'TimeSlotCacheService',
				'TimeLogCacheService',
				'StatusIconCacheService',
				'ImageCacheService',
				'LanguageCacheService',
				'UserOrganizationCacheService'
			];

			// Clear localStorage keys that match these service patterns
			const allKeys = Object.keys(localStorage);
			let clearedCount = 0;

			allKeys.forEach((key) => {
				cacheServicePrefixes.forEach((prefix) => {
					// Cache services use hashed prefixes, so we look for patterns
					if (key.includes(prefix) || key.includes(prefix.toLowerCase())) {
						localStorage.removeItem(key);
						clearedCount++;
					}
				});
			});

			console.log(`Cleared ${clearedCount} cache entries from localStorage`);

			// Strategy 2: Force clear common cache patterns
			this.clearCommonCachePatterns();
		} catch (error) {
			console.warn('Error clearing AbstractCacheServices:', error);
		}
	}

	/**
	 * Clear common cache patterns that might persist
	 */
	private clearCommonCachePatterns(): void {
		const commonPatterns = [
			'_cache',
			'cache_',
			'Cache',
			'CACHE',
			'organization',
			'project',
			'team',
			'employee',
			'task',
			'client',
			'tag',
			'timeslot',
			'timelog'
		];

		const allKeys = Object.keys(localStorage);
		let clearedCount = 0;

		allKeys.forEach((key) => {
			commonPatterns.forEach((pattern) => {
				if (
					key.toLowerCase().includes(pattern.toLowerCase()) &&
					!['themeName', 'preferredLanguage', 'serverConnection'].includes(key)
				) {
					localStorage.removeItem(key);
					clearedCount++;
				}
			});
		});

		console.log(`Cleared ${clearedCount} additional cache patterns from localStorage`);
	}

	/**
	 * Force reload permissions to ensure UI components update correctly
	 */
	private async forcePermissionsReload(): Promise<void> {
		try {
			// 1. Clear NgxPermissionsService again (double-check)
			this.ngxPermissionsService.flushPermissions();

			// 2. Clear store permissions again (double-check)
			this.store.userRolePermissions = [];

			// 3. Force reload permissions from server (this bypasses cache)
			await this.permissionsService.loadPermissions();

			// 4. AGGRESSIVE: Force complete Angular change detection
			this.forceCompleteUIUpdate();
		} catch (error) {
			console.warn('Error reloading permissions:', error);
		}
	}

	/**
	 * Force complete UI update using multiple strategies
	 */
	private forceCompleteUIUpdate(): void {
		// Strategy 1: Force application-wide change detection
		this.applicationRef.tick();

		// Strategy 2: Multiple timeouts with different intervals
		setTimeout(() => {
			this.applicationRef.tick();
			console.log('UI Update - Phase 1: ApplicationRef.tick()');
		}, 0);

		setTimeout(() => {
			this.applicationRef.tick();
			console.log('UI Update - Phase 2: Second tick');
		}, 100);

		setTimeout(() => {
			this.applicationRef.tick();
			console.log('UI Update - Phase 3: Third tick');
		}, 300);

		setTimeout(() => {
			this.applicationRef.tick();
			console.log('UI Update - Phase 4: Final tick - All components should be updated');
		}, 600);

		// Strategy 3: Force store observables to emit
		setTimeout(() => {
			// Trigger store observables by temporarily setting to empty then back
			const currentPermissions = this.store.userRolePermissions;
			this.store.userRolePermissions = [];
			setTimeout(() => {
				this.store.userRolePermissions = currentPermissions;
				console.log('UI Update - Phase 5: Store observables forced to emit');
			}, 50);
		}, 200);

		// Strategy 4: Force NgxPermissionsService to re-emit
		setTimeout(() => {
			this.forceNgxPermissionsUpdate();
		}, 400);
	}

	/**
	 * Force NgxPermissionsService to completely refresh and re-emit
	 */
	private forceNgxPermissionsUpdate(): void {
		try {
			// Get current permissions from store
			const permissions = this.store.userRolePermissions?.map((p) => p.permission) || [];

			// Strategy 1: Complete flush and reload
			this.ngxPermissionsService.flushPermissions();
			this.ngxPermissionsService.loadPermissions(permissions);

			// Strategy 2: Force re-emit by adding/removing a dummy permission
			setTimeout(() => {
				this.ngxPermissionsService.addPermission('DUMMY_PERMISSION_FOR_REFRESH');
				setTimeout(() => {
					this.ngxPermissionsService.removePermission('DUMMY_PERMISSION_FOR_REFRESH');
					console.log('UI Update - Phase 6: NgxPermissionsService forced refresh completed');
				}, 10);
			}, 50);
		} catch (error) {
			console.warn('Error forcing NgxPermissions update:', error);
		}
	}
}
