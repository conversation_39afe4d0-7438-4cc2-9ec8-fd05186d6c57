import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { IAuthResponse } from '@gauzy/contracts';
import { Store, AuthService, ToastrService } from '@gauzy/ui-core/core';

/**
 * Service for handling workspace switching with complete reset
 * Similar to logout/login process
 */
@Injectable({
	providedIn: 'root'
})
export class WorkspaceResetService {
	constructor(
		private readonly store: Store,
		private readonly authService: AuthService,
		private readonly toastrService: ToastrService
	) {}

	/**
	 * Switch workspace with complete reset (simple approach with window reload)
	 *
	 * @param workspaceId The ID of the workspace to switch to
	 * @returns Observable with the auth response
	 */
	public switchWorkspace(workspaceId: string): Observable<IAuthResponse> {
		// 1. Save important user preferences before reset
		const preferredLanguage = this.store.preferredLanguage;
		const themeName = localStorage.getItem('themeName');

		// 2. Call API to switch workspace FIRST (using current token)
		return this.authService.switchWorkspace(workspaceId).pipe(
			tap((response: IAuthResponse) => {
				if (!response) {
					throw new Error('Failed to switch workspace');
				}

				// 3. Update store with new workspace data
				this.initializeState(response);

				// 4. Restore user preferences
				this.store.preferredLanguage = preferredLanguage;
				if (themeName) {
					localStorage.setItem('themeName', themeName);
				}

				// 5. Reload the entire page to ensure fresh state
				// This is the simplest and most reliable approach
				window.location.reload();
			}),
			catchError((error) => {
				console.error('Error switching workspace:', error);
				this.toastrService.danger('Failed to switch workspace. Please try again.', 'Error');
				throw error;
			})
		);
	}

	/**
	 * Initialize application state with new workspace data (like auth-strategy.login)
	 */
	private initializeState(response: IAuthResponse): void {
		const { user, token, refresh_token } = response;

		// Update store exactly like auth-strategy.login (lines 315-320)
		this.store.userId = user.id;
		this.store.token = token;
		this.store.refresh_token = refresh_token;
		this.store.organizationId = user?.employee?.organizationId;
		this.store.tenantId = user?.tenantId;
		this.store.user = user;

		// Note: localStorage is handled by the store itself in this application
		// No need to manually set localStorage items
	}
}
