import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { IAuthResponse } from '@gauzy/contracts';
import { Store, AuthService, ToastrService, TimeTrackerService, TimesheetFilterService } from '@gauzy/ui-core/core';

/**
 * Service for handling workspace switching with complete reset
 * Similar to logout/login process
 */
@Injectable({
	providedIn: 'root'
})
export class WorkspaceResetService {
	constructor(
		private readonly store: Store,
		private readonly authService: AuthService,
		private readonly router: Router,
		private readonly toastrService: ToastrService,
		private readonly timeTrackerService: TimeTrackerService,
		private readonly timesheetFilterService: TimesheetFilterService
	) {}

	/**
	 * Switch workspace with complete reset (following auth-strategy logout/login pattern)
	 *
	 * @param workspaceId The ID of the workspace to switch to
	 * @returns Observable with the auth response
	 */
	public switchWorkspace(workspaceId: string): Observable<IAuthResponse> {
		// 1. Save important user preferences before reset (like auth-strategy._logout)
		const preferredLanguage = this.store.preferredLanguage;
		const themeName = localStorage.getItem('themeName');

		// 2. Call API to switch workspace FIRST (using current token)
		return this.authService.switchWorkspace(workspaceId).pipe(
			tap((response: IAuthResponse) => {
				if (!response) {
					throw new Error('Failed to switch workspace');
				}

				// 3. NOW do pre-logout actions (like auth-strategy._preLogout)
				this.preLogoutActions();

				// 4. Clear store (like auth-strategy._logout)
				this.store.clear();
				this.store.serverConnection = 200;

				// 5. Restore user preferences (like auth-strategy._logout)
				this.store.preferredLanguage = preferredLanguage;
				if (themeName) {
					localStorage.setItem('themeName', themeName);
				}

				// 6. Initialize state with new workspace data (like auth-strategy.login)
				this.initializeState(response);

				// 7. Navigate to dashboard to refresh the app
				this.router.navigate(['/pages/dashboard']);
			}),
			catchError((error) => {
				console.error('Error switching workspace:', error);
				this.toastrService.danger('Failed to switch workspace. Please try again.', 'Error');
				throw error;
			})
		);
	}

	/**
	 * Performs pre-logout actions (like auth-strategy._preLogout)
	 * Clears time tracking and timesheet filter, and deletes cookies
	 */
	private preLogoutActions(): void {
		// Clear time tracking/timesheet filter just before logout (like auth-strategy._preLogout)
		if (this.store.user && this.store.user.employee) {
			if (this.timeTrackerService.running) {
				if (this.timeTrackerService.timerSynced.isExternalSource) {
					this.timeTrackerService.remoteToggle();
				} else {
					this.timeTrackerService.toggle();
				}
			}

			this.timeTrackerService.clearTimeTracker();
			this.timesheetFilterService.clear();
		}

		// Delete cookies (like auth-strategy._preLogout)
		this.deleteCookie('userId');
		this.deleteCookie('token');
		this.deleteCookie('refresh_token');

		// Clear localStorage and sessionStorage
		localStorage.clear();
		sessionStorage.clear();
	}

	/**
	 * Simple function to delete a cookie (since we can't import deleteCookie)
	 */
	private deleteCookie(name: string): void {
		document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; SameSite=None; Secure`;
	}

	/**
	 * Initialize application state with new workspace data (like auth-strategy.login)
	 */
	private initializeState(response: IAuthResponse): void {
		const { user, token, refresh_token } = response;

		// Update store exactly like auth-strategy.login (lines 315-320)
		this.store.userId = user.id;
		this.store.token = token;
		this.store.refresh_token = refresh_token;
		this.store.organizationId = user?.employee?.organizationId;
		this.store.tenantId = user?.tenantId;
		this.store.user = user;

		// Note: localStorage is handled by the store itself in this application
		// No need to manually set localStorage items
	}
}
