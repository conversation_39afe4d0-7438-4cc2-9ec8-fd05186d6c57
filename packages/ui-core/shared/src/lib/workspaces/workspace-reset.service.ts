import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { IAuthResponse } from '@gauzy/contracts';
import { Store, AuthService, ToastrService } from '@gauzy/ui-core/core';

/**
 * Service for handling workspace switching with complete reset
 * Similar to logout/login process
 */
@Injectable({
	providedIn: 'root'
})
export class WorkspaceResetService {
	constructor(
		private readonly store: Store,
		private readonly authService: AuthService,
		private readonly router: Router,
		private readonly toastrService: ToastrService
	) {}

	/**
	 * Switch workspace with complete reset (logout/login approach)
	 *
	 * @param workspaceId The ID of the workspace to switch to
	 * @returns Observable with the auth response
	 */
	public switchWorkspace(workspaceId: string): Observable<IAuthResponse> {
		// 1. Save important user preferences before reset
		const preferredLanguage = this.store.preferredLanguage;
		const themeName = localStorage.getItem('themeName');

		// 2. Call API to switch workspace FIRST (using current token)
		return this.authService.switchWorkspace(workspaceId).pipe(
			tap((response: IAuthResponse) => {
				if (!response) {
					throw new Error('Failed to switch workspace');
				}

				// 4. NOW clear all state (like logout) AFTER successful API call
				this.clearAllState();

				// 5. Restore user preferences
				this.store.preferredLanguage = preferredLanguage;
				if (themeName) {
					localStorage.setItem('themeName', themeName);
				}

				// 6. Initialize state with new workspace data (like login)
				this.initializeState(response);

				// 7. Navigate to dashboard to refresh the app
				this.router.navigate(['/pages/dashboard']);
			}),
			catchError((error) => {
				console.error('Error switching workspace:', error);
				this.toastrService.danger('Failed to switch workspace. Please try again.', 'Error');
				throw error;
			})
		);
	}

	/**
	 * Clear all application state (similar to logout)
	 */
	private clearAllState(): void {
		// 1. Clear store
		this.store.clear();

		// 2. Clear localStorage (except for user preferences)
		this.clearLocalStorage();

		// 3. Clear session storage
		sessionStorage.clear();
	}

	/**
	 * Clear localStorage but preserve user preferences
	 */
	private clearLocalStorage(): void {
		// Save user preferences
		const preferredLanguage = localStorage.getItem('preferredLanguage');
		const themeName = localStorage.getItem('themeName');

		// Clear all localStorage
		localStorage.clear();

		// Restore user preferences
		if (preferredLanguage) {
			localStorage.setItem('preferredLanguage', preferredLanguage);
		}
		if (themeName) {
			localStorage.setItem('themeName', themeName);
		}
	}

	/**
	 * Initialize application state with new workspace data (similar to login)
	 */
	private initializeState(response: IAuthResponse): void {
		const { user, token, refresh_token } = response;

		// 1. Update store
		this.store.user = user;
		this.store.token = token;
		if (refresh_token) {
			this.store.refresh_token = refresh_token;
		}
		this.store.userId = user.id;
		this.store.tenantId = user.tenantId;
		if (user.employee?.organizationId) {
			this.store.organizationId = user.employee.organizationId;
		}

		// 2. Update localStorage
		localStorage.setItem('token', token);
		localStorage.setItem('_userId', user.id);
		localStorage.setItem('_tenantId', user.tenantId);
		if (user.employee?.organizationId) {
			localStorage.setItem('_organizationId', user.employee.organizationId);
		}
	}
}
