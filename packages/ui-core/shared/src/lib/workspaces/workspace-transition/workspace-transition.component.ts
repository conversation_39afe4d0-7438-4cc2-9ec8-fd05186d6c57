import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbSpinnerModule } from '@nebular/theme';
import { BehaviorSubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { WorkspaceSwitchService } from '@gauzy/ui-core/core';

/**
 * Component that displays a smooth transition animation during workspace switching
 * Similar to <PERSON><PERSON><PERSON>'s workspace transition experience
 */
@UntilDestroy({ checkProperties: true })
@Component({
	selector: 'ngx-workspace-transition',
	standalone: true,
	imports: [CommonModule, NbSpinnerModule],
	template: `
		<div
			class="workspace-transition-overlay"
			*ngIf="visible$ | async"
			[ngClass]="{
				visible: (visible$ | async),
				'phase-preparation': (phase$ | async) === 'preparation',
				'phase-loading': (phase$ | async) === 'loading',
				'phase-applying': (phase$ | async) === 'applying',
				'phase-finalizing': (phase$ | async) === 'finalizing'
			}"
		>
			<div class="transition-content">
				<div class="workspace-logo" *ngIf="workspaceLogo$ | async">
					<img [src]="workspaceLogo$ | async" alt="Workspace Logo" />
				</div>
				<div class="workspace-name" *ngIf="workspaceName$ | async">
					{{ workspaceName$ | async }}
				</div>
				<nb-spinner status="primary" size="large"></nb-spinner>
				<div class="phase-indicator">
					{{ phaseMessage$ | async }}
				</div>
				<div class="progress-bar">
					<div class="progress-fill" [style.width.%]="progress$ | async"></div>
				</div>
			</div>
		</div>
	`,
	styles: [
		`
			.workspace-transition-overlay {
				position: fixed;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background-color: rgba(0, 0, 0, 0.85);
				z-index: 9999;
				display: flex;
				justify-content: center;
				align-items: center;
				opacity: 0;
				visibility: hidden;
				transition: opacity 0.3s ease, visibility 0.3s ease;
			}

			.workspace-transition-overlay.visible {
				opacity: 1;
				visibility: visible;
			}

			.transition-content {
				text-align: center;
				color: white;
				max-width: 400px;
				padding: 2rem;
			}

			.workspace-logo {
				margin-bottom: 1rem;
			}

			.workspace-logo img {
				max-width: 120px;
				max-height: 120px;
				border-radius: 16px;
				box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
			}

			.workspace-name {
				font-size: 1.5rem;
				font-weight: bold;
				margin-bottom: 1.5rem;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.phase-indicator {
				margin-top: 1rem;
				font-size: 0.9rem;
				opacity: 0.8;
			}

			.progress-bar {
				margin-top: 1rem;
				height: 4px;
				background-color: rgba(255, 255, 255, 0.2);
				border-radius: 2px;
				overflow: hidden;
			}

			.progress-fill {
				height: 100%;
				background-color: #0088cc;
				transition: width 0.3s ease;
			}

			/* Phase-specific animations */
			.phase-preparation .workspace-logo {
				animation: pulse 1.5s infinite;
			}

			.phase-loading .workspace-logo {
				animation: bounce 1.5s infinite;
			}

			.phase-applying .workspace-logo {
				animation: spin 1.5s infinite;
			}

			.phase-finalizing .workspace-logo {
				animation: grow 1.5s infinite;
			}

			@keyframes pulse {
				0% {
					transform: scale(1);
				}
				50% {
					transform: scale(1.05);
				}
				100% {
					transform: scale(1);
				}
			}

			@keyframes bounce {
				0%,
				100% {
					transform: translateY(0);
				}
				50% {
					transform: translateY(-10px);
				}
			}

			@keyframes spin {
				0% {
					transform: rotate(0deg);
				}
				100% {
					transform: rotate(360deg);
				}
			}

			@keyframes grow {
				0%,
				100% {
					transform: scale(1);
				}
				50% {
					transform: scale(1.1);
				}
			}
		`
	]
})
export class WorkspaceTransitionComponent implements OnInit, OnDestroy {
	// Workspace information
	@Input() set workspaceLogo(value: string) {
		this.workspaceLogo$.next(value);
	}

	@Input() set workspaceName(value: string) {
		this.workspaceName$.next(value);
	}

	// Observable streams
	public visible$ = new BehaviorSubject<boolean>(false);
	public workspaceLogo$ = new BehaviorSubject<string | null>(null);
	public workspaceName$ = new BehaviorSubject<string | null>(null);
	public phase$ = new BehaviorSubject<string>('');
	public progress$ = new BehaviorSubject<number>(0);
	public phaseMessage$ = new BehaviorSubject<string>('');

	// Phase messages
	private readonly phaseMessages = {
		preparation: 'Preparing workspace...',
		loading: 'Loading workspace data...',
		applying: 'Applying workspace settings...',
		finalizing: 'Finalizing...'
	};

	// Cleanup
	private readonly ngDestroy$ = new Subject<void>();

	constructor(private workspaceSwitchService: WorkspaceSwitchService) {}

	ngOnInit(): void {
		// Subscribe to workspace switch status
		this.workspaceSwitchService.switchStatus$.pipe(untilDestroyed(this)).subscribe((status) => {
			this.visible$.next(status.inProgress);

			if (status.inProgress) {
				this.phase$.next(status.phase);
				this.progress$.next(status.progress);
				this.phaseMessage$.next(this.phaseMessages[status.phase] || 'Switching workspace...');
			}
		});
	}

	ngOnDestroy(): void {
		this.ngDestroy$.next();
		this.ngDestroy$.complete();
	}

	/**
	 * Show the transition overlay manually
	 */
	show(workspaceName?: string, workspaceLogo?: string): void {
		if (workspaceName) {
			this.workspaceName$.next(workspaceName);
		}

		if (workspaceLogo) {
			this.workspaceLogo$.next(workspaceLogo);
		}

		this.visible$.next(true);
	}

	/**
	 * Hide the transition overlay manually
	 */
	hide(): void {
		this.visible$.next(false);
	}
}
