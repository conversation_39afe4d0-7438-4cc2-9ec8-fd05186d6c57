import { NgModule } from '@angular/core';
import { WorkspaceContextManager } from './workspace-context.manager';
import { WorkspaceCacheManager } from './workspace-cache.manager';
import { WorkspaceSwitchService } from './workspace-switch.service';

/**
 * Module for workspace-related services
 */
@NgModule({
	providers: [
		WorkspaceContextManager,
		WorkspaceCacheManager,
		WorkspaceSwitchService
	]
})
export class WorkspaceModule {}
