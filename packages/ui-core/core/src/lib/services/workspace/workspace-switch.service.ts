import { Injectable, NgZone } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, of, throwError, timer, from } from 'rxjs';
import { catchError, finalize, switchMap, tap, map } from 'rxjs/operators';
import { Store } from '../store/store.service';
import { WorkspaceContextManager } from './workspace-context.manager';
import { WorkspaceCacheManager } from './workspace-cache.manager';
import {
	WorkspaceSwitchOptions,
	WorkspaceSwitchResult,
	WorkspaceData,
	WorkspaceContext,
	WorkspaceMetadata
} from './types/workspace.types';
import { IAuthResponse, ITenant, IUser } from '@gauzy/contracts';
import { ToastrService } from '../toastr.service';
import { TranslateService } from '@ngx-translate/core';

/**
 * Service for orchestrating workspace switching with optimized phases
 * Similar to how <PERSON><PERSON><PERSON> handles workspace switching
 */
@Injectable({
	providedIn: 'root'
})
export class WorkspaceSwitchService {
	// Default options for workspace switching
	private readonly DEFAULT_OPTIONS: WorkspaceSwitchOptions = {
		preserveUIState: true,
		forceRefresh: false,
		preloadRelated: true,
		transitionDuration: 300,
		showLoading: true
	};

	// Switch status
	private switchInProgress = false;
	private switchStatusSubject = new BehaviorSubject<{
		inProgress: boolean;
		workspaceId: string | null;
		phase: string;
		progress: number;
	}>({
		inProgress: false,
		workspaceId: null,
		phase: '',
		progress: 0
	});
	public switchStatus$ = this.switchStatusSubject.asObservable();

	constructor(
		private contextManager: WorkspaceContextManager,
		private cacheManager: WorkspaceCacheManager,
		private store: Store,
		private router: Router,
		private ngZone: NgZone,
		private toastrService: ToastrService,
		private translateService: TranslateService
	) {}

	/**
	 * Switch to a different workspace with optimized phases
	 */
	switchWorkspace(
		workspaceId: string,
		options: Partial<WorkspaceSwitchOptions> = {}
	): Observable<WorkspaceSwitchResult> {
		// Merge with default options
		const mergedOptions: WorkspaceSwitchOptions = {
			...this.DEFAULT_OPTIONS,
			...options
		};

		// Prevent multiple switches at the same time
		if (this.switchInProgress) {
			return throwError(() => new Error('Workspace switch already in progress'));
		}

		// Don't switch if already on this workspace
		if (workspaceId === this.store.tenantId && !mergedOptions.forceRefresh) {
			return of({
				success: true,
				context: this.contextManager.getCurrentContext(),
				duration: 0,
				fromCache: true
			});
		}

		// Start switch process
		this.switchInProgress = true;
		const startTime = Date.now();

		// Update status
		this.updateSwitchStatus(true, workspaceId, 'preparing', 0);

		// Execute the switch process
		return this.executeSwitchProcess(workspaceId, mergedOptions).pipe(
			tap((result) => {
				// Calculate duration
				const duration = Date.now() - startTime;
				result.duration = duration;

				// Show success message
				if (result.success) {
					const workspaceName = result.context?.metadata.name || 'workspace';
					this.showSuccessMessage(workspaceName);
				}

				// Update status
				this.updateSwitchStatus(false, null, '', 100);
			}),
			catchError((error) => {
				// Show error message
				this.showErrorMessage(error);

				// Update status
				this.updateSwitchStatus(false, null, '', 0);

				return throwError(() => error);
			}),
			finalize(() => {
				this.switchInProgress = false;
			})
		);
	}

	/**
	 * Execute the workspace switch process in phases
	 */
	private executeSwitchProcess(
		workspaceId: string,
		options: WorkspaceSwitchOptions
	): Observable<WorkspaceSwitchResult> {
		return of(null).pipe(
			// Phase 1: Preparation (save current context)
			switchMap(() => this.executePreparationPhase(workspaceId, options)),

			// Phase 2: Data Loading (from cache or API)
			switchMap((context) => this.executeDataLoadingPhase(workspaceId, context, options)),

			// Phase 3: Application (apply new context and data)
			switchMap(({ context, data, fromCache }) =>
				this.executeApplicationPhase(workspaceId, context, data, options, fromCache)
			),

			// Phase 4: Finalization (background tasks)
			switchMap((result) => this.executeFinalizationPhase(workspaceId, result, options))
		);
	}

	/**
	 * Phase 1: Preparation - Save current context and prepare for switch
	 */
	private executePreparationPhase(
		workspaceId: string,
		options: WorkspaceSwitchOptions
	): Observable<WorkspaceContext | null> {
		this.updateSwitchStatus(true, workspaceId, 'preparation', 10);

		// Save current context if user is logged in
		if (this.store.token && this.store.tenantId) {
			return from(this.contextManager.saveCurrentContext()).pipe(
				map(() => {
					// Check if we already have a context for the target workspace
					return this.contextManager.getContext(workspaceId);
				})
			);
		}

		return of(this.contextManager.getContext(workspaceId));
	}

	/**
	 * Phase 2: Data Loading - Load workspace data from cache or API
	 */
	private executeDataLoadingPhase(
		workspaceId: string,
		existingContext: WorkspaceContext | null,
		options: WorkspaceSwitchOptions
	): Observable<{
		context: WorkspaceContext | null;
		data: WorkspaceData;
		fromCache: boolean;
	}> {
		this.updateSwitchStatus(true, workspaceId, 'loading', 30);

		// Try to load from cache first unless force refresh is requested
		return this.cacheManager.loadWorkspaceData(workspaceId, options.forceRefresh).pipe(
			map((data) => {
				const fromCache = !options.forceRefresh && this.cacheManager.isCached(workspaceId);
				return { context: existingContext, data, fromCache };
			})
		);
	}

	/**
	 * Phase 3: Application - Apply the new workspace context and data
	 */
	private executeApplicationPhase(
		workspaceId: string,
		existingContext: WorkspaceContext | null,
		data: WorkspaceData,
		options: WorkspaceSwitchOptions,
		fromCache: boolean
	): Observable<WorkspaceSwitchResult> {
		this.updateSwitchStatus(true, workspaceId, 'applying', 60);

		// Apply the workspace data to the store
		this.applyWorkspaceData(data);

		// Create or update context
		return from(this.createOrUpdateContext(workspaceId, existingContext, data)).pipe(
			switchMap((context) => {
				// Apply the context
				return from(this.contextManager.loadContext(workspaceId, options)).pipe(
					map(() => ({
						success: true,
						context,
						fromCache
					}))
				);
			})
		);
	}

	/**
	 * Phase 4: Finalization - Execute background tasks
	 */
	private executeFinalizationPhase(
		workspaceId: string,
		result: WorkspaceSwitchResult,
		options: WorkspaceSwitchOptions
	): Observable<WorkspaceSwitchResult> {
		this.updateSwitchStatus(true, workspaceId, 'finalizing', 90);

		// Preload related workspaces if requested
		if (options.preloadRelated) {
			this.preloadRelatedWorkspaces();
		}

		// Add a small delay for smooth transition
		return timer(options.transitionDuration || 0).pipe(map(() => result));
	}

	/**
	 * Apply workspace data to the application state
	 */
	private applyWorkspaceData(data: WorkspaceData): void {
		const { authData, orgData } = data;
		const { user, token, refresh_token } = authData;

		// Update store with authentication data
		this.store.token = token;
		if (refresh_token) {
			this.store.refresh_token = refresh_token;
		}
		this.store.userId = user.id;
		this.store.tenantId = user.tenantId;
		this.store.user = user;

		// Update organization data if available
		if (user.employee?.organizationId) {
			this.store.organizationId = user.employee.organizationId;
		}

		if (orgData) {
			this.store.selectedOrganization = orgData;
		}

		// Update localStorage for critical items
		localStorage.setItem('token', token);
		localStorage.setItem('_userId', user.id);
		localStorage.setItem('_tenantId', user.tenantId);
		if (user.employee?.organizationId) {
			localStorage.setItem('_organizationId', user.employee.organizationId);
		}
	}

	/**
	 * Create or update workspace context
	 */
	private async createOrUpdateContext(
		workspaceId: string,
		existingContext: WorkspaceContext | null,
		data: WorkspaceData
	): Promise<WorkspaceContext> {
		const { authData } = data;
		const { user } = authData;

		// Create metadata
		const metadata: WorkspaceMetadata = {
			tenant: user.tenant,
			user: user,
			organization: user.employee?.organization,
			name: user.tenant?.name || 'Workspace',
			logoUrl: user.tenant?.logo,
			isActive: true
		};

		// Create new context or update existing
		if (existingContext) {
			existingContext.metadata = metadata;
			return existingContext;
		} else {
			return await this.contextManager.createContext(workspaceId, metadata);
		}
	}

	/**
	 * Preload related workspaces
	 */
	private preloadRelatedWorkspaces(): void {
		// Load all user workspaces and preload them
		this.cacheManager.preloadAllUserWorkspaces().subscribe();
	}

	/**
	 * Update switch status
	 */
	private updateSwitchStatus(inProgress: boolean, workspaceId: string | null, phase: string, progress: number): void {
		this.switchStatusSubject.next({
			inProgress,
			workspaceId,
			phase,
			progress
		});
	}

	/**
	 * Show success message
	 */
	private showSuccessMessage(workspaceName: string): void {
		this.ngZone.run(() => {
			const message = this.translateService.instant('WORKSPACES.SWITCH_SUCCESS', {
				workspaceName
			});
			this.toastrService.success(message);
		});
	}

	/**
	 * Show error message
	 */
	private showErrorMessage(error: any): void {
		this.ngZone.run(() => {
			const message = this.translateService.instant('WORKSPACES.SWITCH_ERROR');
			this.toastrService.danger(message);
			console.error('Workspace switch error:', error);
		});
	}

	/**
	 * Get current switch status
	 */
	getSwitchStatus(): { inProgress: boolean; workspaceId: string | null; phase: string; progress: number } {
		return this.switchStatusSubject.value;
	}

	/**
	 * Check if switch is in progress
	 */
	isSwitchInProgress(): boolean {
		return this.switchInProgress;
	}
}
