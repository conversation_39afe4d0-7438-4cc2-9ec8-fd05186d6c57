import { IUser, IOrganization, IAuthResponse, ITenant } from '@gauzy/contracts';

/**
 * Represents the complete context of a workspace
 */
export interface WorkspaceContext {
	/** Unique identifier for the workspace (tenantId) */
	id: string;
	
	/** Workspace metadata */
	metadata: WorkspaceMetadata;
	
	/** Current store state for this workspace */
	storeState: WorkspaceStoreState;
	
	/** UI state preservation */
	uiState: WorkspaceUIState;
	
	/** Router state */
	routerState: string;
	
	/** Timestamp when context was last saved */
	lastSaved: Date;
	
	/** Cache expiration time */
	expiresAt: Date;
}

/**
 * Workspace metadata information
 */
export interface WorkspaceMetadata {
	/** Tenant information */
	tenant: ITenant;
	
	/** User information in this workspace */
	user: IUser;
	
	/** Organization information */
	organization?: IOrganization;
	
	/** Workspace name */
	name: string;
	
	/** Workspace logo URL */
	logoUrl?: string;
	
	/** Whether workspace is currently active */
	isActive: boolean;
}

/**
 * Store state specific to a workspace
 */
export interface WorkspaceStoreState {
	/** Authentication tokens */
	auth: {
		token: string;
		refresh_token: string;
		userId: string;
		tenantId: string;
		organizationId?: string;
	};
	
	/** User preferences */
	preferences: {
		language?: string;
		theme?: string;
		layout?: any;
	};
	
	/** Selected entities */
	selected: {
		organization?: IOrganization;
		project?: any;
		team?: any;
	};
	
	/** Feature flags and permissions */
	features: {
		permissions: string[];
		featureToggles: any[];
	};
}

/**
 * UI state that should be preserved across workspace switches
 */
export interface WorkspaceUIState {
	/** Sidebar state */
	sidebar: {
		collapsed: boolean;
		selectedMenu?: string;
	};
	
	/** Current page state */
	page: {
		scrollPosition: number;
		filters?: any;
		searchQuery?: string;
	};
	
	/** Modal and dialog states */
	modals: {
		openModals: string[];
		modalData?: any;
	};
	
	/** Form states */
	forms: {
		unsavedChanges: boolean;
		formData?: any;
	};
}

/**
 * Complete workspace data loaded from API
 */
export interface WorkspaceData {
	/** Authentication response */
	authData: IAuthResponse;
	
	/** Organization data */
	orgData?: IOrganization;
	
	/** User preferences */
	userPrefs: any;
	
	/** Additional workspace-specific data */
	additionalData?: any;
}

/**
 * Cache entry for workspace data
 */
export interface WorkspaceCacheEntry {
	/** Cached data */
	data: WorkspaceData;
	
	/** Cache timestamp */
	timestamp: Date;
	
	/** Cache expiration */
	expiresAt: Date;
	
	/** Cache version for invalidation */
	version: string;
	
	/** Whether data is currently being refreshed */
	isRefreshing: boolean;
}

/**
 * Workspace switch options
 */
export interface WorkspaceSwitchOptions {
	/** Whether to preserve current UI state */
	preserveUIState?: boolean;
	
	/** Whether to force refresh data */
	forceRefresh?: boolean;
	
	/** Whether to preload related workspaces */
	preloadRelated?: boolean;
	
	/** Custom transition duration */
	transitionDuration?: number;
	
	/** Whether to show loading indicator */
	showLoading?: boolean;
}

/**
 * Workspace switch result
 */
export interface WorkspaceSwitchResult {
	/** Whether switch was successful */
	success: boolean;
	
	/** New workspace context */
	context?: WorkspaceContext;
	
	/** Error if switch failed */
	error?: Error;
	
	/** Switch duration in milliseconds */
	duration: number;
	
	/** Whether data was loaded from cache */
	fromCache: boolean;
}

/**
 * Workspace preload status
 */
export interface WorkspacePreloadStatus {
	/** Workspace ID */
	workspaceId: string;
	
	/** Preload status */
	status: 'pending' | 'loading' | 'loaded' | 'error';
	
	/** Progress percentage */
	progress: number;
	
	/** Error if preload failed */
	error?: Error;
}

/**
 * Events emitted during workspace operations
 */
export interface WorkspaceEvents {
	/** Workspace switch started */
	'workspace:switch:start': { workspaceId: string; options: WorkspaceSwitchOptions };
	
	/** Workspace switch completed */
	'workspace:switch:complete': { result: WorkspaceSwitchResult };
	
	/** Workspace switch failed */
	'workspace:switch:error': { workspaceId: string; error: Error };
	
	/** Workspace context saved */
	'workspace:context:saved': { workspaceId: string; context: WorkspaceContext };
	
	/** Workspace data cached */
	'workspace:cache:updated': { workspaceId: string; data: WorkspaceData };
	
	/** Workspace preload status changed */
	'workspace:preload:status': { status: WorkspacePreloadStatus };
}
