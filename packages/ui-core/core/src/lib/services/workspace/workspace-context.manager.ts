import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject } from 'rxjs';
import { Store } from '../store/store.service';
import {
	WorkspaceContext,
	WorkspaceStoreState,
	WorkspaceUIState,
	WorkspaceMetadata,
	WorkspaceSwitchOptions
} from './types/workspace.types';

/**
 * Manages workspace contexts with state preservation and restoration
 * Similar to how <PERSON><PERSON><PERSON> manages workspace switching
 */
@Injectable({
	providedIn: 'root'
})
export class WorkspaceContextManager {
	private contexts = new Map<string, WorkspaceContext>();
	private currentContextId: string | null = null;
	private readonly CONTEXT_EXPIRY_HOURS = 24;
	private readonly MAX_CONTEXTS = 10;

	// Observables for context changes
	private currentContextSubject = new BehaviorSubject<WorkspaceContext | null>(null);
	public currentContext$ = this.currentContextSubject.asObservable();

	constructor(private store: Store, private router: Router) {
		this.initializeContextManager();
	}

	/**
	 * Initialize the context manager
	 */
	private initializeContextManager(): void {
		// Load contexts from localStorage on startup
		this.loadContextsFromStorage();

		// Set up periodic cleanup
		this.setupPeriodicCleanup();

		// Initialize current context if user is logged in
		if (this.store.token && this.store.tenantId) {
			this.initializeCurrentContext();
		}
	}

	/**
	 * Get the current active context
	 */
	getCurrentContext(): WorkspaceContext | null {
		return this.currentContextSubject.value;
	}

	/**
	 * Get context by workspace ID
	 */
	getContext(workspaceId: string): WorkspaceContext | null {
		return this.contexts.get(workspaceId) || null;
	}

	/**
	 * Check if context exists for workspace
	 */
	hasContext(workspaceId: string): boolean {
		return this.contexts.has(workspaceId);
	}

	/**
	 * Save current workspace context
	 */
	async saveCurrentContext(): Promise<void> {
		if (!this.currentContextId) {
			return;
		}

		try {
			const context: WorkspaceContext = {
				id: this.currentContextId,
				metadata: this.captureWorkspaceMetadata(),
				storeState: this.captureStoreState(),
				uiState: this.captureUIState(),
				routerState: this.router.url,
				lastSaved: new Date(),
				expiresAt: new Date(Date.now() + this.CONTEXT_EXPIRY_HOURS * 60 * 60 * 1000)
			};

			this.contexts.set(this.currentContextId, context);
			this.saveContextsToStorage();

			console.log(`Context saved for workspace: ${this.currentContextId}`);
		} catch (error) {
			console.error('Error saving workspace context:', error);
		}
	}

	/**
	 * Load and apply workspace context
	 */
	async loadContext(workspaceId: string, options: WorkspaceSwitchOptions = {}): Promise<WorkspaceContext | null> {
		try {
			const context = this.contexts.get(workspaceId);

			if (!context) {
				console.log(`No context found for workspace: ${workspaceId}`);
				return null;
			}

			// Check if context is expired
			if (this.isContextExpired(context)) {
				console.log(`Context expired for workspace: ${workspaceId}`);
				this.contexts.delete(workspaceId);
				this.saveContextsToStorage();
				return null;
			}

			// Apply the context
			await this.applyContext(context, options);

			this.currentContextId = workspaceId;
			this.currentContextSubject.next(context);

			console.log(`Context loaded for workspace: ${workspaceId}`);
			return context;
		} catch (error) {
			console.error('Error loading workspace context:', error);
			return null;
		}
	}

	/**
	 * Create new context for workspace
	 */
	async createContext(workspaceId: string, metadata: WorkspaceMetadata): Promise<WorkspaceContext> {
		const context: WorkspaceContext = {
			id: workspaceId,
			metadata,
			storeState: this.createDefaultStoreState(),
			uiState: this.createDefaultUIState(),
			routerState: '/pages/dashboard',
			lastSaved: new Date(),
			expiresAt: new Date(Date.now() + this.CONTEXT_EXPIRY_HOURS * 60 * 60 * 1000)
		};

		this.contexts.set(workspaceId, context);
		this.saveContextsToStorage();

		return context;
	}

	/**
	 * Remove context for workspace
	 */
	removeContext(workspaceId: string): void {
		this.contexts.delete(workspaceId);
		this.saveContextsToStorage();

		if (this.currentContextId === workspaceId) {
			this.currentContextId = null;
			this.currentContextSubject.next(null);
		}
	}

	/**
	 * Clear all contexts
	 */
	clearAllContexts(): void {
		this.contexts.clear();
		this.currentContextId = null;
		this.currentContextSubject.next(null);
		localStorage.removeItem('workspace_contexts');
	}

	/**
	 * Get all available contexts
	 */
	getAllContexts(): WorkspaceContext[] {
		return Array.from(this.contexts.values());
	}

	/**
	 * Capture current workspace metadata
	 */
	private captureWorkspaceMetadata(): WorkspaceMetadata {
		const user = this.store.user;
		const selectedOrg = this.store.selectedOrganization;

		return {
			tenant: user?.tenant || ({ id: this.store.tenantId, name: 'Unknown' } as any),
			user: user,
			organization: selectedOrg,
			name: user?.tenant?.name || selectedOrg?.name || 'Workspace',
			logoUrl: user?.tenant?.logo || selectedOrg?.imageUrl,
			isActive: true
		};
	}

	/**
	 * Capture current store state
	 */
	private captureStoreState(): WorkspaceStoreState {
		return {
			auth: {
				token: this.store.token || '',
				refresh_token: this.store.refresh_token || '',
				userId: this.store.userId || '',
				tenantId: this.store.tenantId || '',
				organizationId: this.store.organizationId
			},
			preferences: {
				language: this.store.preferredLanguage,
				theme: localStorage.getItem('themeName'),
				layout: this.store.preferredComponentLayout
			},
			selected: {
				organization: this.store.selectedOrganization,
				project: this.store.selectedProject,
				team: this.store.selectedTeam
			},
			features: {
				permissions: (this.store.userRolePermissions || []).map((p) =>
					typeof p === 'string' ? p : p.permission
				),
				featureToggles: this.store.featureToggles || []
			}
		};
	}

	/**
	 * Capture current UI state
	 */
	private captureUIState(): WorkspaceUIState {
		return {
			sidebar: {
				collapsed: this.getSidebarState(),
				selectedMenu: this.getSelectedMenu()
			},
			page: {
				scrollPosition: window.scrollY || 0,
				filters: this.getPageFilters(),
				searchQuery: this.getSearchQuery()
			},
			modals: {
				openModals: this.getOpenModals(),
				modalData: this.getModalData()
			},
			forms: {
				unsavedChanges: this.hasUnsavedChanges(),
				formData: this.getFormData()
			}
		};
	}

	/**
	 * Apply context to current application state
	 */
	private async applyContext(context: WorkspaceContext, options: WorkspaceSwitchOptions): Promise<void> {
		// Apply store state
		this.applyStoreState(context.storeState);

		// Apply UI state if requested
		if (options.preserveUIState !== false) {
			this.applyUIState(context.uiState);
		}

		// Navigate to saved route if different
		if (context.routerState && context.routerState !== this.router.url) {
			await this.router.navigateByUrl(context.routerState);
		}
	}

	/**
	 * Apply store state from context
	 */
	private applyStoreState(storeState: WorkspaceStoreState): void {
		// Apply auth state
		this.store.token = storeState.auth.token;
		this.store.refresh_token = storeState.auth.refresh_token;
		this.store.userId = storeState.auth.userId;
		this.store.tenantId = storeState.auth.tenantId;
		this.store.organizationId = storeState.auth.organizationId;

		// Apply preferences
		if (storeState.preferences.language) {
			this.store.preferredLanguage = storeState.preferences.language;
		}
		if (storeState.preferences.layout) {
			this.store.preferredComponentLayout = storeState.preferences.layout;
		}

		// Apply selected entities
		if (storeState.selected.organization) {
			this.store.selectedOrganization = storeState.selected.organization;
		}
		if (storeState.selected.project) {
			this.store.selectedProject = storeState.selected.project;
		}
		if (storeState.selected.team) {
			this.store.selectedTeam = storeState.selected.team;
		}

		// Apply features (convert string permissions back to objects if needed)
		// Note: This is a simplified approach - in production you might need to reconstruct full permission objects
		this.store.userRolePermissions = storeState.features.permissions.map((p) => ({ permission: p } as any));
		this.store.featureToggles = storeState.features.featureToggles;
	}

	/**
	 * Apply UI state from context
	 */
	private applyUIState(uiState: WorkspaceUIState): void {
		// Apply sidebar state
		this.applySidebarState(uiState.sidebar);

		// Apply page state
		this.applyPageState(uiState.page);

		// Apply modal state
		this.applyModalState(uiState.modals);

		// Apply form state
		this.applyFormState(uiState.forms);
	}

	// Helper methods for UI state management
	private getSidebarState(): boolean {
		// Implementation depends on your sidebar component
		return localStorage.getItem('sidebar_collapsed') === 'true';
	}

	private getSelectedMenu(): string | undefined {
		return localStorage.getItem('selected_menu') || undefined;
	}

	private getPageFilters(): any {
		// Implementation depends on your filter system
		return {};
	}

	private getSearchQuery(): string | undefined {
		// Implementation depends on your search system
		return undefined;
	}

	private getOpenModals(): string[] {
		// Implementation depends on your modal system
		return [];
	}

	private getModalData(): any {
		// Implementation depends on your modal system
		return undefined;
	}

	private hasUnsavedChanges(): boolean {
		// Implementation depends on your form system
		return false;
	}

	private getFormData(): any {
		// Implementation depends on your form system
		return undefined;
	}

	private applySidebarState(sidebar: any): void {
		if (sidebar.collapsed !== undefined) {
			localStorage.setItem('sidebar_collapsed', sidebar.collapsed.toString());
		}
		if (sidebar.selectedMenu) {
			localStorage.setItem('selected_menu', sidebar.selectedMenu);
		}
	}

	private applyPageState(page: any): void {
		if (page.scrollPosition) {
			setTimeout(() => window.scrollTo(0, page.scrollPosition), 100);
		}
	}

	private applyModalState(_: any): void {
		// Implementation depends on your modal system
	}

	private applyFormState(_: any): void {
		// Implementation depends on your form system
	}

	/**
	 * Create default store state for new workspace
	 */
	private createDefaultStoreState(): WorkspaceStoreState {
		return {
			auth: {
				token: '',
				refresh_token: '',
				userId: '',
				tenantId: '',
				organizationId: undefined
			},
			preferences: {
				language: this.store.preferredLanguage,
				theme: localStorage.getItem('themeName'),
				layout: this.store.preferredComponentLayout
			},
			selected: {
				organization: undefined,
				project: undefined,
				team: undefined
			},
			features: {
				permissions: [],
				featureToggles: []
			}
		};
	}

	/**
	 * Create default UI state for new workspace
	 */
	private createDefaultUIState(): WorkspaceUIState {
		return {
			sidebar: {
				collapsed: false,
				selectedMenu: undefined
			},
			page: {
				scrollPosition: 0,
				filters: undefined,
				searchQuery: undefined
			},
			modals: {
				openModals: [],
				modalData: undefined
			},
			forms: {
				unsavedChanges: false,
				formData: undefined
			}
		};
	}

	/**
	 * Check if context is expired
	 */
	private isContextExpired(context: WorkspaceContext): boolean {
		return new Date() > context.expiresAt;
	}

	/**
	 * Initialize current context from store
	 */
	private initializeCurrentContext(): void {
		if (this.store.tenantId) {
			this.currentContextId = this.store.tenantId;

			// Try to load existing context or create new one
			const existingContext = this.contexts.get(this.store.tenantId);
			if (existingContext && !this.isContextExpired(existingContext)) {
				this.currentContextSubject.next(existingContext);
			} else {
				// Create new context for current state
				const metadata = this.captureWorkspaceMetadata();
				this.createContext(this.store.tenantId, metadata).then((context) => {
					this.currentContextSubject.next(context);
				});
			}
		}
	}

	/**
	 * Load contexts from localStorage
	 */
	private loadContextsFromStorage(): void {
		try {
			const stored = localStorage.getItem('workspace_contexts');
			if (stored) {
				const contexts = JSON.parse(stored);
				Object.entries(contexts).forEach(([id, context]: [string, any]) => {
					// Convert date strings back to Date objects
					context.lastSaved = new Date(context.lastSaved);
					context.expiresAt = new Date(context.expiresAt);

					// Only load non-expired contexts
					if (!this.isContextExpired(context)) {
						this.contexts.set(id, context);
					}
				});
			}
		} catch (error) {
			console.error('Error loading contexts from storage:', error);
		}
	}

	/**
	 * Save contexts to localStorage
	 */
	private saveContextsToStorage(): void {
		try {
			// Cleanup expired contexts before saving
			this.cleanupExpiredContexts();

			// Limit number of contexts
			this.limitContexts();

			const contextsObj = Object.fromEntries(this.contexts);
			localStorage.setItem('workspace_contexts', JSON.stringify(contextsObj));
		} catch (error) {
			console.error('Error saving contexts to storage:', error);
		}
	}

	/**
	 * Setup periodic cleanup of expired contexts
	 */
	private setupPeriodicCleanup(): void {
		// Cleanup every hour
		setInterval(() => {
			this.cleanupExpiredContexts();
		}, 60 * 60 * 1000);
	}

	/**
	 * Remove expired contexts
	 */
	private cleanupExpiredContexts(): void {
		const now = new Date();
		const expiredIds: string[] = [];

		this.contexts.forEach((context, id) => {
			if (context.expiresAt < now) {
				expiredIds.push(id);
			}
		});

		expiredIds.forEach((id) => {
			this.contexts.delete(id);
		});

		if (expiredIds.length > 0) {
			console.log(`Cleaned up ${expiredIds.length} expired workspace contexts`);
		}
	}

	/**
	 * Limit number of stored contexts
	 */
	private limitContexts(): void {
		if (this.contexts.size <= this.MAX_CONTEXTS) {
			return;
		}

		// Sort by last saved date and keep only the most recent
		const sortedContexts = Array.from(this.contexts.entries()).sort(
			([, a], [, b]) => b.lastSaved.getTime() - a.lastSaved.getTime()
		);

		// Keep only the most recent contexts
		const toKeep = sortedContexts.slice(0, this.MAX_CONTEXTS);
		const toRemove = sortedContexts.slice(this.MAX_CONTEXTS);

		// Clear and rebuild with limited contexts
		this.contexts.clear();
		toKeep.forEach(([id, context]) => {
			this.contexts.set(id, context);
		});

		console.log(`Limited workspace contexts: kept ${toKeep.length}, removed ${toRemove.length}`);
	}
}
